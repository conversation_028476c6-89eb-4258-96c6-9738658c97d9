const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if cuisine attributes already exist
    const existingCuisines = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type = 'cuisine'`,
      { type: QueryTypes.SELECT }
    );

    if (existingCuisines.length === 0) {
      // Cuisine Types
      const cuisineTypes = [
        {
          name: "Italian",
          slug: "italian",
          description: "Traditional cuisine from Italy featuring pasta, pizza, risotto, and Mediterranean ingredients."
        },
        {
          name: "Mexican",
          slug: "mexican",
          description: "Vibrant cuisine from Mexico with bold flavors, spices, and ingredients like chili, lime, and cilantro."
        },
        {
          name: "Chinese",
          slug: "chinese",
          description: "Diverse cuisine from China with regional variations, featuring stir-fries, dumplings, and balanced flavors."
        },
        {
          name: "Indian",
          slug: "indian",
          description: "Rich and aromatic cuisine from India with complex spice blends, curries, and diverse regional dishes."
        },
        {
          name: "French",
          slug: "french",
          description: "Classic European cuisine known for refined techniques, sauces, and elegant presentation."
        },
        {
          name: "Japanese",
          slug: "japanese",
          description: "Traditional cuisine from Japan emphasizing fresh ingredients, simplicity, and seasonal flavors."
        },
        {
          name: "Thai",
          slug: "thai",
          description: "Southeast Asian cuisine balancing sweet, sour, salty, and spicy flavors with fresh herbs."
        },
        {
          name: "Mediterranean",
          slug: "mediterranean",
          description: "Healthy cuisine from the Mediterranean region featuring olive oil, fresh vegetables, and seafood."
        },
        {
          name: "American",
          slug: "american",
          description: "Diverse cuisine from the United States with regional specialties and fusion influences."
        },
        {
          name: "British",
          slug: "british",
          description: "Traditional cuisine from Britain featuring hearty dishes, roasts, and classic comfort foods."
        },
        {
          name: "Spanish",
          slug: "spanish",
          description: "Vibrant cuisine from Spain featuring tapas, paella, and bold Mediterranean flavors."
        },
        {
          name: "Greek",
          slug: "greek",
          description: "Ancient cuisine from Greece with olive oil, feta cheese, fresh herbs, and grilled meats."
        },
        {
          name: "Korean",
          slug: "korean",
          description: "Flavorful cuisine from Korea featuring fermented foods, spicy dishes, and balanced nutrition."
        },
        {
          name: "Vietnamese",
          slug: "vietnamese",
          description: "Fresh and healthy cuisine from Vietnam with herbs, rice noodles, and light broths."
        },
        {
          name: "Middle Eastern",
          slug: "middle-eastern",
          description: "Rich cuisine from the Middle East featuring spices, grains, and traditional cooking methods."
        }
      ];

      // Prepare bulk insert data
      const cuisineData = cuisineTypes.map(cuisine => ({
        attribute_title: cuisine.name,
        attribute_slug: cuisine.slug,
        attribute_description: cuisine.description,
        attribute_type: "cuisine",
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert("mo_food_attributes", cuisineData);
      console.log("✅ Cuisine Attributes seeded successfully");
    } else {
      console.log("⏭️  Cuisine Attributes already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: "cuisine"
    });
  }
};
