{"name": "backend-recipe-ms", "version": "1.0.0", "description": "This README would normally document whatever steps are necessary to get your application up and running.", "main": "index.js", "scripts": {"start:dev": "nodemon", "dev": "ts-node --files ./src/index.ts", "build": "npx tsc -p . && npx shx cp -r src/locales build/src/locales", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "prepare": "husky", "upload-samples": "ts-node scripts/uploadSampleFiles.ts", "db:migrate": "npx sequelize-cli db:migrate --env development", "db:migrate:dev": "npx sequelize-cli db:migrate --env development", "db:migrate:staging": "npx sequelize-cli db:migrate --env staging", "db:migrate:prod": "npx sequelize-cli db:migrate --env production", "db:seed": "npx sequelize-cli db:seed:all --env development", "db:seed:dev": "npx sequelize-cli db:seed:all --env development", "db:seed:staging": "npx sequelize-cli db:seed:all --env staging", "db:seed:prod": "npx sequelize-cli db:seed:all --env production", "db:status": "npx sequelize-cli db:migrate:status --env development", "db:undo": "npx sequelize-cli db:migrate:undo --env development", "seeder:all": "npx sequelize-cli db:seed:all", "test:keploy": "jest --coverage --coverageReporters=text --coverageReporters=cobertura --collectCoverageFrom='./**/*.{js,jsx,ts,tsx}'"}, "lint-staged": {"*": "npm run lint"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "@types/pdfkit": "^0.13.9", "amqplib": "^0.10.5", "axios": "^1.9.0", "backend-rota-ms": "file:", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-session": "^1.18.1", "form-data": "^4.0.3", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "keycloak-connect": "^26.0.7", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.5", "node-cron": "^3.0.3", "path": "^0.12.7", "pdfkit": "^0.17.1", "puppeteer": "^23.11.1", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.3", "stripe": "^17.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "yamljs": "^0.3.0", "xlsx": "^0.18.5", "csv-writer": "^1.6.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/i18n": "^0.13.12", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^22.10.2", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/yamljs": "^0.2.34", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^8.57.1", "husky": "^9.1.7", "lint-staged": "^15.4.3", "nodemon": "^3.1.7", "prettier": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.2", "shx": "^0.3.4"}}